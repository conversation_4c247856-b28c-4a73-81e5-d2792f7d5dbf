package vn.vnpt.digo.reporter.service.statistics;

import org.bson.Document;
import org.bson.types.ObjectId;
import org.jxls.builder.xls.XlsCommentAreaBuilder;
import org.jxls.util.JxlsHelper;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.configurationprocessor.json.JSONArray;
import org.springframework.boot.configurationprocessor.json.JSONException;
import org.springframework.core.io.Resource;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;
import vn.vnpt.digo.reporter.dto.qni.AgencyFilterReportQniResponse;
import vn.vnpt.digo.reporter.dto.qni.DetailGeneralReportDto;
import vn.vnpt.digo.reporter.dto.qni.GeneralReportDto;
import vn.vnpt.digo.reporter.service.AgencyFilterReportQniService;
import vn.vnpt.digo.reporter.util.*;

import java.util.*;
import java.util.stream.Collectors;

import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service
public class StatisticsGeneralService {
    private static final RestTemplate restTemplate = new RestTemplate();
    Logger logger = org.slf4j.LoggerFactory.getLogger(StatisticsGeneralService.class);
    DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
    TimeZone timezone = TimeZone.getTimeZone("GMT");
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private Microservice microservice;

    @Autowired
    private Translator translator;

    @Value(value = Constant.LOCATION_EXCEL_DOSSIER_STATISTIC_ASSIGNEE_QNI)
    private Resource resourceTemplateDossierStatisticAssigneeQNI;

    @Value(value = Constant.LOCATION_EXCEL_DOSSIER_STATISTIC_ASSIGNEE_QNI_v2)
    private Resource resourceTemplateDossierStatisticAssigneeQNIV2;

    @Autowired
    private AgencyFilterReportQniService agencyFilterReportQniService;

    @Value("${vnpt.permission.role.admin}")
    private String adminRoles;

    @Value("${digo.enable.hide.fullname}")
    private boolean enableHideName;

    private static List<ObjectId> convertToObjectIdList(List<String> hexStrings) {
        return hexStrings.stream()
                .map(ObjectId::new)
                .collect(Collectors.toList());
    }

    private Document hasField(String field) {
        return new Document("$ifNull", Arrays.asList(field, false));
    }

    private Document notHasField(String field) {
        return new Document("$not", hasField(field));
    }

    private Document khongPhaiHoSoHuy() {
        return new Document("$or", Arrays.asList(
                new Document("$ne", Arrays.asList("$dossierStatusId", 6)),
                new Document("$and", Arrays.asList(
                        new Document("$eq", Arrays.asList("$dossierStatusId", 6)),
                        hasField("$withdrawDate")
                ))
        ));
    }

    private Document dateAfter(Object d1, Object d2) {
        return new Document("$gt", Arrays.asList(d1, d2));
    }

    private Document dateBefore(Object d1, Object d2) {
        return new Document("$lt", Arrays.asList(d1, d2));
    }

    private Document dateEquals(Object d1, Object d2) {
        return new Document("$eq", Arrays.asList(d1, d2));
    }

    private Document dateEqualsOrBefore(Object d1, Object d2) {
        return new Document("$lte", Arrays.asList(d1, d2));
    }

    private Document elemAt(String arrayField, int idx) {
        return new Document("$arrayElemAt", Arrays.asList(arrayField, idx));
    }

    private Document dateBetween(String field, Date from, Date to) {
        return new Document("$and", Arrays.asList(
                new Document("$gte", Arrays.asList(field, from)),
                new Document("$lte", Arrays.asList(field, to))
        ));
    }

    private Document isNotCancelledBeforeDate(Date date) {
        return new Document("$or", Arrays.asList(
                new Document("$and", Arrays.asList(
                        hasField("$cancelledDate"),
                        dateAfter("$cancelledDate", date)
                )),
                notHasField("$cancelledDate")
        ));
    }

    private Document isNotWithdrawnBeforeDate(Date date) {
        return new Document("$or", Arrays.asList(
                new Document("$and", Arrays.asList(
                        hasField("$withdrawDate"),
                        dateAfter("$withdrawDate", date)
                )),
                notHasField("$withdrawDate")
        ));
    }

    private Document isAcceptedBeforeOrOnDate(Date date) {
        return new Document("$and", Arrays.asList(
                hasField("$acceptedDate"),
                dateEqualsOrBefore("$acceptedDate", date)
        ));
    }

    private Document getDossierStatusConditionForAdditionalDate(Object toDate) {
        Document additional0 = elemAt("$additionalDate", 0);

        return new Document("$or", Arrays.asList(
                new Document("$and", Arrays.asList(
                        new Document("$ne", Arrays.asList("$dossierStatusId", 1)),
                        new Document("$or", Arrays.asList(
                                dateAfter(additional0, "$appointmentDate"),
                                dateAfter(toDate, "$appointmentDate")
                        ))
                )),
                new Document("$and", Arrays.asList(
                        new Document("$eq", Arrays.asList("$dossierStatusId", 1)),
                        dateAfter(additional0, "$appointmentDate")
                ))
        ));
    }


    private Document buildLateProcessingCondition(Object referenceDate, boolean isKyTruoc) {
        Document additional0 = elemAt("$additionalDate", 0);


        Document case1;
        if (isKyTruoc && referenceDate instanceof Date) {
            case1 = new Document("$and", Arrays.asList(
                    notHasField("$financialObligationsDate"),
                    notHasField("$additionalDate.0"),
                    hasField("$appointmentDate"),
                    dateStringComparison("$gt", "$appointmentDate", referenceDate)
            ));
        } else {
            case1 = new Document("$and", Arrays.asList(
                    notHasField("$financialObligationsDate"),
                    notHasField("$additionalDate.0"),
                    hasField("$appointmentDate"),
                    dateAfter(referenceDate, "$appointmentDate")
            ));
        }


        Document case2 = new Document("$and", Arrays.asList(
                hasField("$financialObligationsDate"),
                hasField("$appointmentDate"),
                dateAfter("$financialObligationsDate", "$appointmentDate"),
                notHasField("$additionalDate.0")
        ));

        Document case3;
        if (isKyTruoc) {
            case3 = new Document("$and", Arrays.asList(
                    hasField("$additionalDate.0"),
                    hasField("$appointmentDate"),
                    dateAfter(additional0, "$appointmentDate"),
                    notHasField("$financialObligationsDate")
            ));
        } else {
            case3 = new Document("$and", Arrays.asList(
                    hasField("$additionalDate.0"),
                    hasField("$appointmentDate"),
                    getDossierStatusConditionForAdditionalDate(referenceDate),
                    notHasField("$financialObligationsDate")
            ));
        }

        Document case4;
        if (isKyTruoc) {
            case4 = new Document("$and", Arrays.asList(
                    hasField("$financialObligationsDate"),
                    hasField("$additionalDate.0"),
                    hasField("$appointmentDate"),
                    dateAfter("$financialObligationsDate", "$appointmentDate")
            ));
        } else {
            case4 = new Document("$and", Arrays.asList(
                    hasField("$financialObligationsDate"),
                    hasField("$additionalDate.0"),
                    hasField("$appointmentDate"),
                    new Document("$or", Arrays.asList(
                            dateAfter("$financialObligationsDate", "$appointmentDate"),
                            dateAfter(additional0, "$appointmentDate")
                    ))
            ));
        }

        return new Document("$or", Arrays.asList(case1, case2, case3, case4));
    }

    private Document buildLateProcessingCondition(Object referenceDate) {
        return buildLateProcessingCondition(referenceDate, false);
    }

    private Document buildLateProcessingConditionWithKyTruoc(Date referenceDate) {
        return buildLateProcessingCondition(referenceDate, true);
    }

    public Document documentDangGiaiQuyetQuaHan(Date toDate) {
        return new Document("$and", Arrays.asList(
                khongPhaiHoSoHuy(),
                isAcceptedBeforeOrOnDate(toDate),
                hasValidProcessingTime(),
                isNotCancelledBeforeDate(toDate),
                isNotWithdrawnBeforeDate(toDate),
                new Document("$or", Arrays.asList(
                        new Document("$and", Arrays.asList(
                                notHasField("$completedDate"),
                                buildLateProcessingCondition(toDate)
                        )),
                        new Document("$and", Arrays.asList(
                                dateAfter("$completedDate", toDate),
                                buildLateProcessingCondition(toDate)
                        ))
                ))
        ));
    }

    public Document getDangGiaiQuyetQuaHan(Date toDate) {
        return new Document("$cond", Arrays.asList(
                documentDangGiaiQuyetQuaHan(toDate),
                1, 0)
        );
    }

    public Document documentTiepNhanTrucTuyen(Date fromDate, Date toDate) {
        return new Document("$and", Arrays.asList(
                hasField("$acceptedDate"),
                dateBetween("$acceptedDate", fromDate, toDate),
                new Document("$eq", Arrays.asList("$applyMethodId", 0)),
                khongPhaiHoSoHuy(),
                hasField("$appointmentDate")
        ));
    }

    public Document getTiepNhanTrucTuyen(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                documentTiepNhanTrucTuyen(fromDate, toDate),
                1, 0)
        );
    }

    public Document documentTiepNhanKyTruocGiaiQuyetQuaHan(Date previousDate) {
        return new Document("$and", Arrays.asList(
                khongPhaiHoSoHuy(),
                isAcceptedBeforeOrOnDateString(previousDate),
                hasValidProcessingTime(),
                isNotCancelledBeforeDateString(previousDate),
                isNotWithdrawnBeforeDateString(previousDate),
                new Document("$or", Arrays.asList(
                        new Document("$and", Arrays.asList(
                                notHasField("$completedDate"),
                                buildLateProcessingConditionWithKyTruoc(previousDate)
                        )),
                        new Document("$and", Arrays.asList(
                                hasField("$completedDate"),
                                dateStringComparison("$lt", "$completedDate", previousDate),
                                buildLateProcessingConditionWithKyTruoc(previousDate)
                        ))
                ))
        ));
    }

    public Document getTiepNhanKyTruocGiaiQuyetQuaHan(Date previousDate) {
        return new Document("$cond", Arrays.asList(
                documentTiepNhanKyTruocGiaiQuyetQuaHan(previousDate),
                1, 0)
        );
    }

    private Document dateStringComparison(String operator, Object field1, Object field2) {
        Document dateStr1, dateStr2;


        if (field1 instanceof String) {
            dateStr1 = new Document("$dateToString",
                    new Document("format", "%Y-%m-%d").append("date", field1));
        } else {
            dateStr1 = new Document("$dateToString",
                    new Document("format", "%Y-%m-%d").append("date", field1));
        }


        if (field2 instanceof String) {
            dateStr2 = new Document("$dateToString",
                    new Document("format", "%Y-%m-%d").append("date", field2));
        } else {
            dateStr2 = new Document("$dateToString",
                    new Document("format", "%Y-%m-%d").append("date", field2));
        }

        return new Document(operator, Arrays.asList(dateStr1, dateStr2));
    }

    private Document isAcceptedBeforeOrOnDateString(Date date) {
        return new Document("$and", Arrays.asList(
                hasField("$acceptedDate"),
                dateStringComparison("$lte", "$acceptedDate", date)
        ));
    }

    private Document isNotCancelledBeforeDateString(Date date) {
        return new Document("$or", Arrays.asList(
                new Document("$and", Arrays.asList(
                        hasField("$cancelledDate"),
                        dateStringComparison("$gt", "$cancelledDate", date)
                )),
                notHasField("$cancelledDate")
        ));
    }

    private Document isNotWithdrawnBeforeDateString(Date date) {
        return new Document("$or", Arrays.asList(
                new Document("$and", Arrays.asList(
                        hasField("$withdrawDate"),
                        dateStringComparison("$gt", "$withdrawDate", date)
                )),
                notHasField("$withdrawDate")
        ));
    }

    public Document documentTiepNhanKyTruocGiaiQuyetTrongHan(Date previousDate) {
        return new Document("$and", Arrays.asList(
                khongPhaiHoSoHuy(),
                isAcceptedBeforeOrOnDateString(previousDate),
                isNotCancelledBeforeDateString(previousDate),
                isNotWithdrawnBeforeDateString(previousDate),
                new Document("$or", Arrays.asList(
                        new Document("$and", Arrays.asList(
                                new Document("$eq", Arrays.asList("$processingTime", 0)),
                                new Document("$or", Arrays.asList(
                                        new Document("$and", Arrays.asList(
                                                hasField("$completedDate"),
                                                dateStringComparison("$gt", "$completedDate", previousDate)
                                        )),
                                        notHasField("$completedDate")
                                ))
                        )),
                        new Document("$and", Arrays.asList(
                                hasValidProcessingTime(),
                                new Document("$or", Arrays.asList(
                                        new Document("$and", Arrays.asList(
                                                notHasField("$completedDate"),
                                                buildWithinDueDateConditionWithKyTruoc(previousDate)
                                        )),
                                        new Document("$and", Arrays.asList(
                                                hasField("$completedDate"),
                                                dateStringComparison("$lt", "$completedDate", previousDate),
                                                buildWithinDueDateConditionWithKyTruoc(previousDate)
                                        ))
                                ))
                        ))
                ))
        ));
    }

    public Document getTiepNhanKyTruoc(Date previousDate) {
        return new Document("$or", Arrays.asList(documentTiepNhanKyTruocGiaiQuyetQuaHan(previousDate), documentTiepNhanKyTruocGiaiQuyetTrongHan(previousDate)));
    }

    public Document getTiepNhan(Date fromDate, Date toDate, Date previousDate) {
        return new Document("$or", Arrays.asList(documentTiepNhanTrucTuyen(fromDate, toDate),
                documentHoSoTiepNhanConLai(fromDate, toDate),
                documentTiepNhanKyTruocGiaiQuyetQuaHan(previousDate),
                documentTiepNhanKyTruocGiaiQuyetTrongHan(previousDate)));
    }

    public Document getDangXuLy(Date toDate) {
        return new Document("$or", Arrays.asList(documentDangGiaiQuyetTrongHan(toDate), documentDangGiaiQuyetQuaHan(toDate)));
    }

    public Document getDaXuLy(Date fromDate, Date toDate) {
        return new Document("$or", Arrays.asList(documentDaXuLyDungHanCondition(fromDate, toDate), documentDaXuLySomHanCondition(fromDate, toDate), documentDaXuLyQuaHanCondition(fromDate, toDate)));
    }

    public Document getTiepNhanKyTruocGiaiQuyetTrongHan(Date previousDate) {
        return new Document("$cond", Arrays.asList(
                documentTiepNhanKyTruocGiaiQuyetTrongHan(previousDate),
                1, 0)
        );
    }

    private Document documentHoSoTiepNhanConLai(Date fromDate, Date toDate) {
        return new Document("$and", Arrays.asList(
                hasField("$acceptedDate"),
                dateBetween("$acceptedDate", fromDate, toDate),
                new Document("$ne", Arrays.asList("$applyMethodId", 0)),
                khongPhaiHoSoHuy(),
                hasField("$appointmentDate")
        ));
    }

    public Document getHoSoTiepNhanConLai(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                documentHoSoTiepNhanConLai(fromDate, toDate),
                1, 0)
        );
    }

    private Document buildWithinDueDateCondition(Object referenceDate, boolean isKyTruoc) {
        Document additional0 = elemAt("$additionalDate", 0);

        Document case1;
        if (isKyTruoc && referenceDate instanceof Date) {
            case1 = new Document("$and", Arrays.asList(
                    notHasField("$financialObligationsDate"),
                    notHasField("$additionalDate.0"),
                    hasField("$appointmentDate"),
                    dateStringComparison("$lte", "$appointmentDate", referenceDate)
            ));
        } else {
            case1 = new Document("$and", Arrays.asList(
                    notHasField("$financialObligationsDate"),
                    notHasField("$additionalDate.0"),
                    hasField("$appointmentDate"),
                    dateEqualsOrBefore(referenceDate, "$appointmentDate")
            ));
        }

        Document case2 = new Document("$and", Arrays.asList(
                hasField("$financialObligationsDate"),
                hasField("$appointmentDate"),
                dateEqualsOrBefore("$financialObligationsDate", "$appointmentDate"),
                notHasField("$additionalDate.0")
        ));

        Document case3;
        if (isKyTruoc) {
            case3 = new Document("$and", Arrays.asList(
                    hasField("$additionalDate.0"),
                    hasField("$appointmentDate"),
                    dateEqualsOrBefore(additional0, "$appointmentDate"),
                    notHasField("$financialObligationsDate")
            ));
        } else {
            case3 = new Document("$and", Arrays.asList(
                    hasField("$additionalDate.0"),
                    hasField("$appointmentDate"),
                    new Document("$or", Arrays.asList(

                            new Document("$and", Arrays.asList(
                                    new Document("$ne", Arrays.asList("$dossierStatusId", 1)),
                                    dateEqualsOrBefore(additional0, "$appointmentDate"),
                                    dateEqualsOrBefore(referenceDate, "$appointmentDate")
                            )),

                            new Document("$and", Arrays.asList(
                                    new Document("$eq", Arrays.asList("$dossierStatusId", 1)),
                                    dateEqualsOrBefore(additional0, "$appointmentDate")
                            ))
                    )),
                    notHasField("$financialObligationsDate")
            ));
        }

        Document case4;
        if (isKyTruoc) {
            case4 = new Document("$and", Arrays.asList(
                    hasField("$financialObligationsDate"),
                    hasField("$additionalDate.0"),
                    hasField("$appointmentDate"),
                    dateEqualsOrBefore("$financialObligationsDate", "$appointmentDate"),
                    dateEqualsOrBefore(additional0, "$appointmentDate")
            ));
        } else {
            case4 = new Document("$and", Arrays.asList(
                    hasField("$financialObligationsDate"),
                    hasField("$additionalDate.0"),
                    hasField("$appointmentDate"),
                    new Document("$and", Arrays.asList(
                            dateEqualsOrBefore("$financialObligationsDate", "$appointmentDate"),
                            dateEqualsOrBefore(additional0, "$appointmentDate")
                    ))
            ));
        }

        if (isKyTruoc) {
            Document case5 = new Document("$and", Arrays.asList(
                    hasField("$financialObligationsDate"),
                    hasField("$appointmentDate"),
                    dateEqualsOrBefore("$financialObligationsDate", "$appointmentDate"),
                    notHasField("$additionalDate")
            ));
            return new Document("$or", Arrays.asList(case1, case2, case3, case4, case5));
        } else {
            return new Document("$or", Arrays.asList(case1, case2, case3, case4));
        }
    }


    private Document buildWithinDueDateCondition(Object referenceDate) {
        return buildWithinDueDateCondition(referenceDate, false);
    }

    private Document buildWithinDueDateConditionWithKyTruoc(Date referenceDate) {
        return buildWithinDueDateCondition(referenceDate, true);
    }

    private Document isNotCompletedOrCompletedAfterDate(Date date) {
        return new Document("$or", Arrays.asList(
                notHasField("$completedDate"),
                dateAfter("$completedDate", date)
        ));
    }

    private Document isCompletedAfterDate(Date date) {
        return new Document("$and", Arrays.asList(
                hasField("$completedDate"),
                dateAfter("$completedDate", date)
        ));
    }

    public Document documentDangGiaiQuyetTrongHan(Date toDate) {
        return new Document("$and", Arrays.asList(
                khongPhaiHoSoHuy(),
                isAcceptedBeforeOrOnDate(toDate),
                isNotCancelledBeforeDate(toDate),
                isNotWithdrawnBeforeDate(toDate),
                new Document("$or", Arrays.asList(

                        new Document("$and", Arrays.asList(
                                new Document("$eq", Arrays.asList("$processingTime", 0)),
                                isNotCompletedOrCompletedAfterDate(toDate)
                        )),

                        new Document("$and", Arrays.asList(
                                hasValidProcessingTime(),
                                new Document("$or", Arrays.asList(

                                        new Document("$and", Arrays.asList(
                                                notHasField("$completedDate"),
                                                buildWithinDueDateCondition(toDate)
                                        )),

                                        new Document("$and", Arrays.asList(
                                                isCompletedAfterDate(toDate),
                                                buildWithinDueDateCondition(toDate)
                                        ))
                                ))
                        ))
                ))
        ));
    }

    public Document getDangGiaiQuyetTrongHan(Date toDate) {
        return new Document("$cond", Arrays.asList(
                documentDangGiaiQuyetTrongHan(toDate),
                1, 0)
        );
    }

    private Document hasValidProcessingTime() {
        return new Document("$or", Arrays.asList(
                new Document("$ne", Arrays.asList("$processingTime", 0)),
                new Document("$eq", Arrays.asList("$processingTime", -1))
        ));
    }

    private Document completedEarlyWithZeroProcessingTime(Date fromDate, Date toDate) {
        return new Document("$and", Arrays.asList(
                hasField("$completedDate"),
                hasField("$acceptedDate"),
                new Document("$eq", Arrays.asList("$processingTime", 0)),
                dateBetween("$completedDate", fromDate, toDate),
                dateEqualsOrBefore("$acceptedDate", toDate)
        ));
    }

    private Document completedEarlyBeforeAppointment(Date fromDate, Date toDate) {
        return new Document("$and", Arrays.asList(
                hasField("$completedDate"),
                hasField("$appointmentDate"),
                dateBetween("$completedDate", fromDate, toDate),
                dateBefore("$completedDate", "$appointmentDate")
        ));
    }

    private Document cancelledEarlyBeforeAppointment(Date fromDate, Date toDate) {
        return new Document("$and", Arrays.asList(
                hasField("$cancelledDate"),
                hasField("$appointmentDate"),
                dateBetween("$cancelledDate", fromDate, toDate),
                notHasField("$completedDate"),
                dateBefore("$cancelledDate", "$appointmentDate")
        ));
    }

    private Document cancelledEarlyBeforeAppointmentNoWithdraw(Date fromDate, Date toDate) {
        return new Document("$and", Arrays.asList(
                hasField("$cancelledDate"),
                hasField("$appointmentDate"),
                dateBetween("$cancelledDate", fromDate, toDate),
                notHasField("$withdrawDate"),
                notHasField("$completedDate"),
                dateBefore("$cancelledDate", "$appointmentDate")
        ));
    }

    public Document documentDaXuLySomHanCondition(Date fromDate, Date toDate) {
        Document baseCondition = new Document("$and", Arrays.asList(
                new Document("$ne", Arrays.asList("$dossierStatusId", 6)),
                notHasField("$withdrawDate")
        ));


        Document case1 = completedEarlyWithZeroProcessingTime(fromDate, toDate);


        Document earlyCompletionScenarios = new Document("$or", Arrays.asList(

                completedEarlyWithZeroProcessingTime(fromDate, toDate),

                completedEarlyBeforeAppointment(fromDate, toDate),

                cancelledEarlyBeforeAppointment(fromDate, toDate),

                cancelledEarlyBeforeAppointmentNoWithdraw(fromDate, toDate)
        ));

        Document case2 = new Document("$and", Arrays.asList(
                hasValidProcessingTime(),
                earlyCompletionScenarios
        ));


        Document mainCondition = new Document("$or", Arrays.asList(case1, case2));

        return new Document("$and", Arrays.asList(baseCondition, mainCondition));
    }

    public Document getDaXuLySomHanCondition(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                documentDaXuLySomHanCondition(fromDate, toDate),
                1, 0)
        );
    }

    private Document completedExactlyOnAppointment(Date fromDate, Date toDate) {
        return new Document("$and", Arrays.asList(
                hasField("$completedDate"),
                hasField("$appointmentDate"),
                dateBetween("$completedDate", fromDate, toDate),
                dateEquals("$completedDate", "$appointmentDate")
        ));
    }

    private Document cancelledExactlyOnAppointmentWithWithdraw(Date fromDate, Date toDate) {
        return new Document("$and", Arrays.asList(
                hasField("$cancelledDate"),
                hasField("$appointmentDate"),
                hasField("$withdrawDate"),
                dateBetween("$cancelledDate", fromDate, toDate),
                dateEquals("$cancelledDate", "$appointmentDate"),
                notHasField("$completedDate")
        ));
    }

    public Document documentDaXuLyDungHanCondition(Date fromDate, Date toDate) {
        return new Document("$and", Arrays.asList(
                new Document("$ne", Arrays.asList("$dossierStatusId", 6)),
                notHasField("$withdrawDate"),
                hasValidProcessingTime(),
                isAcceptedBeforeOrOnDate(toDate),


                new Document("$or", Arrays.asList(

                        completedExactlyOnAppointment(fromDate, toDate),

                        cancelledExactlyOnAppointmentWithWithdraw(fromDate, toDate)
                ))
        ));
    }

    public Document getDaXuLyDungHanCondition(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                documentDaXuLyDungHanCondition(fromDate, toDate),
                1, 0)
        );
    }

    private Document completedAfterAppointment(Date fromDate, Date toDate) {
        return new Document("$and", Arrays.asList(
                hasField("$completedDate"),
                hasField("$appointmentDate"),
                dateBetween("$completedDate", fromDate, toDate),
                dateAfter("$completedDate", "$appointmentDate")
        ));
    }

    private Document withdrawnAfterAppointmentWithCancelled(Date fromDate, Date toDate) {
        return new Document("$and", Arrays.asList(
                hasField("$withdrawDate"),
                hasField("$cancelledDate"),
                hasField("$appointmentDate"),
                dateBetween("$withdrawDate", fromDate, toDate),
                dateBetween("$cancelledDate", fromDate, toDate),
                notHasField("$completedDate"),
                dateAfter("$withdrawDate", "$appointmentDate")
        ));
    }

    private Document withdrawnAfterAppointmentNoCancelled(Date fromDate, Date toDate) {
        return new Document("$and", Arrays.asList(
                hasField("$withdrawDate"),
                hasField("$appointmentDate"),
                dateBetween("$withdrawDate", fromDate, toDate),
                notHasField("$cancelledDate"),
                notHasField("$completedDate"),
                dateAfter("$withdrawDate", "$appointmentDate")
        ));
    }

    private Document cancelledAfterAppointmentNoWithdrawn(Date fromDate, Date toDate) {
        return new Document("$and", Arrays.asList(
                hasField("$cancelledDate"),
                hasField("$appointmentDate"),
                dateBetween("$cancelledDate", fromDate, toDate),
                notHasField("$withdrawDate"),
                notHasField("$completedDate"),
                dateAfter("$cancelledDate", "$appointmentDate")
        ));
    }

    public Document documentDaXuLyQuaHanCondition(Date fromDate, Date toDate) {
        return new Document("$and", Arrays.asList(
                new Document("$ne", Arrays.asList("$dossierStatusId", 6)),
                notHasField("$withdrawDate"),
                hasValidProcessingTime(),
                isAcceptedBeforeOrOnDate(toDate),


                new Document("$or", Arrays.asList(

                        completedAfterAppointment(fromDate, toDate),

                        withdrawnAfterAppointmentWithCancelled(fromDate, toDate),

                        withdrawnAfterAppointmentNoCancelled(fromDate, toDate),

                        cancelledAfterAppointmentNoWithdrawn(fromDate, toDate)
                ))
        ));
    }

    public Document getDaXuLyQuaHanCondition(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                documentDaXuLyQuaHanCondition(fromDate, toDate),
                1, 0)
        );
    }

    private Document buildSubmitFormCondition(
            int applyMethodId,
            List<Integer> includeHinhThucNop,
            List<Integer> excludeHinhThucNop,
            Date fromDate,
            Date toDate) {

        List<Document> conditions = new ArrayList<>(Arrays.asList(
                new Document("$eq", Arrays.asList("$applyMethodId", applyMethodId)),
                hasField("$acceptedDate"),
                dateBetween("$acceptedDate", fromDate, toDate),
                khongPhaiHoSoHuy(),
                hasField("$appointmentDate")
        ));

        if (includeHinhThucNop != null && !includeHinhThucNop.isEmpty()) {
            conditions.add(new Document("$in", Arrays.asList(
                    "$applicantHinhThucNop", includeHinhThucNop
            )));
        }

        if (excludeHinhThucNop != null && !excludeHinhThucNop.isEmpty()) {
            conditions.add(
                    new Document("$not", new Document("$in", Arrays.asList(
                            "$applicantHinhThucNop", excludeHinhThucNop
                    )))
            );
        }

        return new Document("$cond", Arrays.asList(
                new Document("$and", conditions),
                1, 0
        ));
    }

    public Document getHoSoRutCondition(Date fromDate, Date toDate) {
        return new Document("$cond", Arrays.asList(
                new Document("$and", Arrays.asList(
                        hasField("$withdrawDate"),
                        dateBetween("$withdrawDate", fromDate, toDate),
                        hasField("$appointmentDate")
                )),
                1, 0
        ));
    }


    public Document getHoSoTrucTiepCondition(Date fromDate, Date toDate) {
        return buildSubmitFormCondition(
                1,
                null,
                Arrays.asList(3, 4, 5),  // loại bưu chính/app
                fromDate, toDate
        );
    }

    public Document getHoSoBuuChinhCondition(Date fromDate, Date toDate) {
        return buildSubmitFormCondition(
                1,
                List.of(3), null,
                fromDate, toDate
        );
    }

    public Document getHoSoBuuChinhCongIchCondition(Date fromDate, Date toDate) {
        return buildSubmitFormCondition(
                1,
                List.of(4), null,
                fromDate, toDate
        );
    }

    public Document getHoSoSmartPhoneCondition(Date fromDate, Date toDate) {
        return buildSubmitFormCondition(
                0,
                List.of(5), null,
                fromDate, toDate
        );
    }

    public String keyAddField(Integer reportType) {

        String keyAddField = "";
        switch (reportType) {
            case 0:
                keyAddField = "ten_don_vi_cha";
                break;
            case 1:
                keyAddField = "ten_linh_vuc";
                break;
            case 2:
                keyAddField = "ten_thu_tuc";
                break;
            default:
                break;
        }

        return keyAddField;
    }

    public String valueAddField(Integer reportType) {

        String valueAddField = "";
        switch (reportType) {
            case 0:
                valueAddField = "$agencyAncestorsLevel1Name";
                break;
            case 1:
                valueAddField = "$sectorName";
                break;
            case 2:
                valueAddField = "$procedureName";
                break;
            default:
                break;
        }

        return valueAddField;
    }

    public Criteria buildCriteria(List<ObjectId> agencyIds,
                                  Integer reportType,
                                  List<String> sectorId,
                                  List<String> procedureId,
                                  String[] option) throws JSONException {
        List<Criteria> criteriaList = new ArrayList<>();
        Criteria criteriaAgency = null;

        criteriaAgency = new Criteria().orOperator(
                Criteria.where("agencyId").in(agencyIds)
        );
        criteriaList.add(criteriaAgency);

        switch (reportType) {
            case 0: // tổng hợp
                option[0] = "agencyBaby.idAgency";
                option[1] = "ten_don_vi_cha";
                option[2] = "agencyName";
                option[3] = "agencyId";
                break;
            case 1: // lĩnh vực
                option[0] = "sector.idSector";
                option[1] = "ten_linh_vuc";
                option[2] = "sectorName";
                option[3] = "sectorId";

                if (sectorId != null && !sectorId.isEmpty()) {
                    List<ObjectId> sectorObjectIds = convertToObjectIdList(sectorId);
                    Criteria additionalCriteria = new Criteria().andOperator(
                            Criteria.where("sectorId").in(sectorObjectIds)
                    );

                    criteriaList.add(additionalCriteria);
                }

                break;
            case 2: // thủ tục
                option[0] = "procedure.idProcedure";
                option[1] = "ten_thu_tuc";
                option[2] = "procedureName";
                option[3] = "procedureId";

                if (procedureId != null && !procedureId.isEmpty()) {
                    List<ObjectId> procedureObjectIds = convertToObjectIdList(procedureId);
                    Criteria additionalCriteria = new Criteria().andOperator(
                            Criteria.where("procedureId").in(procedureObjectIds)
                    );

                    criteriaList.add(additionalCriteria);
                }

                break;
            default:
                option[0] = "agencyBaby.idAgency";
                option[1] = "ten_don_vi_cha";
                option[2] = "agencyName";
                option[3] = "agencyId";
                break;
        }

        Criteria criteria = new Criteria();
        criteria = criteria.andOperator(criteriaList.toArray(new Criteria[0]));

        return criteria;
    }

    public Document docunemtMatch(Integer type, Date fromDate, Date toDate, Date previousDate) {

        Document document = new Document();

        switch (type) {
            case 3: // tổng tiếp nhận
                document = getTiepNhan(fromDate, toDate, previousDate);
                break;
            case 4: // tiếp nhận trực tuyến
                document = getTiepNhanTrucTuyen(fromDate, toDate);
                break;
            case 5: // lấy hồ sơ tiếp nhận còn lại
                document = getHoSoTiepNhanConLai(fromDate, toDate);
                break;
            case 6: // lấy hồ sơ tiếp nhận kỳ trước
                document = getTiepNhanKyTruoc(previousDate);
                break;
            case 7: // lấy hồ sơ đã giải quyết
                document = getDaXuLy(fromDate, toDate);
                break;
            case 8: // lấy hồ sơ đã giải quyết trước hạn
                document = getDaXuLySomHanCondition(fromDate, toDate);
                break;
            case 9: // lấy hồ sơ đã giải quyết đúng hạn
                document = getDaXuLyDungHanCondition(fromDate, toDate);
                break;
            case 89: // trước hạn + đúng hạn
                break;
            case 10:
                document = getDaXuLyQuaHanCondition(fromDate, toDate);
                break;
            case 11: // lấy hồ sơ đang giải quyết
                document = getDangXuLy(toDate);
                break;
            case 12: // lấy hồ sơ đang giải quyết trong hạn
                document = getDangGiaiQuyetTrongHan(toDate);
                break;
            case 13: // lấy hồ sơ đang giải quyết quá hạn
                document = getDangGiaiQuyetQuaHan(toDate);
                break;
            case 14: // lấy hồ sơ dừng xử lý
                break;
            case 15: // lấy hồ sơ rút
                document = getHoSoRutCondition(fromDate, toDate);
                break;
            case 1415:
                break;
            case 16:
                break;
            case 17: // lấy hồ sơ nhận trực tiếp
                document = getHoSoTrucTiepCondition(fromDate, toDate);
                break;
            case 18: // lấy hồ sơ bưu chính
                document = getHoSoBuuChinhCondition(fromDate, toDate);
                break;
            case 19: // lấy hồ sơ bưu chính công ích
                document = getHoSoBuuChinhCongIchCondition(fromDate, toDate);
                break;
            case 20: // láy hồ sơ smartphone
                document = getHoSoSmartPhoneCondition(fromDate, toDate);
                break;
            case 21: // láy hồ sơ smartphone
                document = getHoSoRutCondition(fromDate, toDate);
                break;
        }

        return document;
    }

    public Aggregation getAggregationDetail(Date fromDate,
                                            Date toDate,
                                            List<ObjectId> agencyIds,
                                            Date previousDate,
                                            Integer reportType,
                                            List<String> sectorId,
                                            List<String> procedureId,
                                            Integer type,
                                            Pageable pageable,
                                            Boolean ignorePagination) throws JSONException {

        String[] option = new String[4];

        AggregationOperation matchOperation = Aggregation.match(buildCriteria(agencyIds, reportType, sectorId, procedureId, option));
        Document documentExpr = new Document("$expr", docunemtMatch(type, fromDate, toDate, previousDate));
        AggregationOperation matchCondition = context -> {
            return new Document("$match", documentExpr);
        };

        AggregationOperation groupOperation = Aggregation.group("_id")
                .first("dossierCode").as("dossierCode")
                .first("acceptedDate").as("acceptedDate")
                .first("appointmentDate").as("appointmentDate")
                .first("completedDate").as("completedDate")
                .first("applicantEFormDataOwnerFullName").as("ownerFullName")
                .first("applicantEFormDataNoiDungYeuCauGiaiQuyet").as("noiDungYeuCauGiaiQuyet")
                .first("applicantPhoneNumber").as("phoneNumber")
                .first("currentTaskAssigneeName").as("assigneeFullname")
                .first("sectorName").as("sectorName")
                .first("procedureName").as("procedureName")
                .first("currentTaskAgencyName").as("curTaskAgencyName")
                .first("dossierStatusName").as("dossierStatusName")
                .first("applyMethodName").as("applyMethod");

        AggregationOperation finalProjectOperation = Aggregation.project()
                .andInclude("_id")
                .and(DateOperators.DateToString.dateOf("acceptedDate").toString("%d-%m-%Y %H:%M:%S").withTimezone(DateOperators.Timezone.valueOf("Asia/Ho_Chi_Minh"))).as("acceptedDate")
                .and(DateOperators.DateToString.dateOf("appointmentDate").toString("%d-%m-%Y %H:%M:%S").withTimezone(DateOperators.Timezone.valueOf("Asia/Ho_Chi_Minh"))).as("appointmentDate")
                .and(DateOperators.DateToString.dateOf("completedDate").toString("%d-%m-%Y %H:%M:%S").withTimezone(DateOperators.Timezone.valueOf("Asia/Ho_Chi_Minh"))).as("completedDate")
                .and("dossierCode").as("dossierCode")
                .and("ownerFullName").as("applicantOwnerFullName")
                .and("noiDungYeuCauGiaiQuyet").as("noiDungYeuCauGiaiQuyet")
                .and("phoneNumber").as("applicantPhoneNumber")
                .and("assigneeFullname").as("assigneeFullname")
                .and("sectorName").as("sectorName")
                .and("procedureName").as("procedureName")
                .and("curTaskAgencyName").as("curTaskAgencyName")
                .and("dossierStatusName").as("dossierStatusName")
                .and("applyMethod").as("applyMethod");

        List<AggregationOperation> aggregationOperations = null;
        if (ignorePagination) {
            aggregationOperations = Arrays.asList(
                    matchOperation,
                    matchCondition,
                    groupOperation,
                    finalProjectOperation
            );
        } else {
            AggregationOperation skipStage = Aggregation.skip((long) pageable.getPageNumber() * pageable.getPageSize());
            AggregationOperation limitStage = Aggregation.limit(pageable.getPageSize());

            AggregationOperation totalCountGroupOperation = Aggregation.group().count().as("totalCount");
            AggregationOperation totalCountProjectOperation = Aggregation.project()
                    .andExclude("_id")
                    .andInclude("totalCount");

            FacetOperation pagedFacet = new FacetOperation().and(groupOperation, finalProjectOperation, skipStage, limitStage).as("pagedResults")
                    .and(totalCountGroupOperation, totalCountProjectOperation).as("totalCount");

            aggregationOperations = Arrays.asList(
                    matchOperation,
                    matchCondition,
                    pagedFacet
            );
        }

        AggregationOptions options = AggregationOptions.builder()
                .allowDiskUse(true)
                .build();
        return Aggregation.newAggregation(aggregationOperations).withOptions(options);
    }

    private List<String> getRootAgencies(List<String> agencyIds) throws JSONException {
        String getObjUrl;
        String jsonString;
        JSONArray jsonArr;

        List<String> rootAgencyIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(agencyIds)) {
            getObjUrl = "qni-agency/--find-root?id=" + String.join(",", agencyIds);
            getObjUrl = microservice.basedataUri(getObjUrl).toUriString();
            try {
                jsonString = MicroserviceExchange.get(restTemplate, getObjUrl, String.class);
                jsonArr = new JSONArray(jsonString);
                for (int i = 0; i < jsonArr.length(); i++) {
                    rootAgencyIds.add(jsonArr.getJSONObject(i).get("id").toString());
                }
            } catch (Exception ex) {
                logger.error("Can not find root agencies ", ex);
            }
        }
        return rootAgencyIds;
    }

    private List<GeneralReportDto.CustomSummary> getReportWithAgency(List<String> agencyIds, List<GeneralReportDto> generalReportDto,
                                                                     Boolean includeInactiveAgency,
                                                                     Boolean isDVC,
                                                                     Date fromDate, Date toDate) throws JSONException {
        Comparator<GeneralReportDto.CustomSummary> agencyComparator = Comparator.comparing(agency -> agency.getAgency().getName());
        var rootAgencys = getRootAgencies(agencyIds);

        var getObjUrl = "";

        if (includeInactiveAgency != null && includeInactiveAgency) {
            getObjUrl = microservice.basedataUri("agency/--by-parent-agency?include-inactive=true&arr-id=" + String.join(",", rootAgencys)).toUriString();
        } else {
            getObjUrl = microservice.basedataUri("agency/--by-parent-agency?arr-id=" + String.join(",", rootAgencys)).toUriString();
        }

        var jsonString = MicroserviceExchange.get(restTemplate, getObjUrl, String.class);
        var jsonArr = new JSONArray(jsonString);

        List<GeneralReportDto.Agency> agencyTransList = new ArrayList<>();

        if (Objects.nonNull(jsonArr)) {
            for (int i = 0; i < jsonArr.length(); i++) {
                String agencyIdTemp = jsonArr.getJSONObject(i).get("id").toString();
                var target = new GeneralReportDto.Agency();
                target.setIdAgency(agencyIdTemp);
                target.setName(jsonArr.getJSONObject(i).get("name").toString());

                agencyTransList.add(target);
            }
        }

        Map<String, Set<String>> agencyFilterCache = agencyTransList.parallelStream()
                .collect(Collectors.toConcurrentMap(
                        agency -> agency.getId(),
                        agency -> {
                            try {
                                AgencyFilterReportQniResponse agencyTemp = agencyFilterReportQniService.getDocumentsByAgencyId(agency.getId());
                                return agencyTemp != null ? uniqueStringAgency(agencyTemp.getIdFilter()) : Collections.emptySet();
                            } catch (Exception e) {
                                // Log error nhưng không break flow
                                return Collections.<String>emptySet();
                            }
                        }
                ));

        // Tối ưu: Group reports by agencyBaby.getId() để tránh scan lặp lại
        Map<String, List<GeneralReportDto>> reportsByAgencyBaby = generalReportDto.stream()
                .filter(report -> report.getAgencyBaby() != null && report.getAgencyBaby().getId() != null)
                .collect(Collectors.groupingBy(report -> report.getAgencyBaby().getId()));

        List<GeneralReportDto.CustomSummary> resultsAgency = new ArrayList<>();
        for (GeneralReportDto.Agency agency : agencyTransList) {
            var target = new GeneralReportDto.CustomSummary(null, null, agency, 0, 0, 0, 0, 0, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 0);

            Set<String> filteredAgencyIds = agencyFilterCache.get(agency.getId());
            if (filteredAgencyIds != null && !filteredAgencyIds.isEmpty()) {
                // Tối ưu: Chỉ process các reports có agencyBaby.getId() trong filteredAgencyIds
                for (String agencyId : filteredAgencyIds) {
                    List<GeneralReportDto> matchingReports = reportsByAgencyBaby.get(agencyId);
                    if (matchingReports != null) {
                        for (GeneralReportDto generalReport : matchingReports) {
                            // Sử dụng helper method để aggregate data
                            aggregateReportData(target, generalReport);
                        }
                    }
                }

                // Lấy số lượng dịch vụ công đang sử dụng phục vụ ở trang DVC
                // Tạm thời bỏ qua, sẽ tính toán sau để tối ưu performance
            }

            resultsAgency.add(target);
        }

        return resultsAgency.stream()
                .filter(this::hasAnyData)
                .sorted(agencyComparator)
                .collect(Collectors.toList());

    }

    public int countDistinctProcedureIds(Date fromDate, Date toDate, List<ObjectId> agencyIds) {

        MatchOperation matchOperation = Aggregation.match(
                Criteria.where("acceptedDate").exists(true)
                        .andOperator(
                                Criteria.where("acceptedDate").gte(fromDate),
                                Criteria.where("acceptedDate").lte(toDate),
                                Criteria.where("agencyId").in(agencyIds)
                        )
        );

        GroupOperation groupOperation = Aggregation.group("procedureId");
        Aggregation aggregation = Aggregation.newAggregation(matchOperation, groupOperation);
        AggregationResults<Document> results = mongoTemplate.aggregate(aggregation, "flattenedDossier", Document.class);

        return results.getMappedResults().size();
    }







    /**
     * Tối ưu cho DVC: Tính procedureUsed ngay trong vòng for, không cần tách riêng
     */
    private List<GeneralReportDto.CustomSummary> getReportWithAgencyForDVC(List<String> agencyIds, List<GeneralReportDto> generalReportDto,
                                                                           Boolean includeInactiveAgency,
                                                                           Date fromDate, Date toDate) throws JSONException {
        Comparator<GeneralReportDto.CustomSummary> agencyComparator = Comparator.comparing(agency -> agency.getAgency().getName());
        var rootAgencys = getRootAgencies(agencyIds);

        var getObjUrl = "";
        if (includeInactiveAgency != null && includeInactiveAgency) {
            getObjUrl = microservice.basedataUri("agency/--by-parent-agency?include-inactive=true&arr-id=" + String.join(",", rootAgencys)).toUriString();
        } else {
            getObjUrl = microservice.basedataUri("agency/--by-parent-agency?arr-id=" + String.join(",", rootAgencys)).toUriString();
        }

        var jsonString = MicroserviceExchange.get(restTemplate, getObjUrl, String.class);
        var jsonArr = new JSONArray(jsonString);

        List<GeneralReportDto.Agency> agencyTransList = new ArrayList<>();
        if (Objects.nonNull(jsonArr)) {
            for (int i = 0; i < jsonArr.length(); i++) {
                String agencyIdTemp = jsonArr.getJSONObject(i).get("id").toString();
                var target = new GeneralReportDto.Agency();
                target.setIdAgency(agencyIdTemp);
                target.setName(jsonArr.getJSONObject(i).get("name").toString());
                agencyTransList.add(target);
            }
        }

        // Tối ưu: Pre-cache agency filter data
        Map<String, Set<String>> agencyFilterCache = new HashMap<>();
        for (GeneralReportDto.Agency agency : agencyTransList) {
            try {
                var agencyTemp = agencyFilterReportQniService.getDocumentsByAgencyId(agency.getId());
                if (agencyTemp != null) {
                    agencyFilterCache.put(agency.getId(), uniqueStringAgency(agencyTemp.getIdFilter()));
                }
            } catch (Exception e) {
                logger.warn("Error caching agency filter for: " + agency.getId(), e);
            }
        }

        // Tối ưu: Group reports by agencyBaby.getId() để tránh scan lặp lại
        Map<String, List<GeneralReportDto>> reportsByAgencyBaby = generalReportDto.stream()
                .filter(report -> report.getAgencyBaby() != null && report.getAgencyBaby().getId() != null)
                .collect(Collectors.groupingBy(report -> report.getAgencyBaby().getId()));

        List<GeneralReportDto.CustomSummary> resultsAgency = new ArrayList<>();
        for (GeneralReportDto.Agency agency : agencyTransList) {
            var target = new GeneralReportDto.CustomSummary(null, null, agency, 0, 0, 0, 0, 0, 0, 0,
                    0, 0, 0, 0, 0, 0, 0, 0, 0, 0);

            Set<String> filteredAgencyIds = agencyFilterCache.get(agency.getId());
            if (filteredAgencyIds != null && !filteredAgencyIds.isEmpty()) {
                // Tối ưu: Chỉ process các reports có agencyBaby.getId() trong filteredAgencyIds
                for (String agencyId : filteredAgencyIds) {
                    List<GeneralReportDto> matchingReports = reportsByAgencyBaby.get(agencyId);
                    if (matchingReports != null) {
                        for (GeneralReportDto generalReport : matchingReports) {
                            // Sử dụng helper method để aggregate data
                            aggregateReportData(target, generalReport);
                        }
                    }
                }

                // Tính procedureUsed ngay tại đây, trong vòng for
                if (hasAnyData(target)) {
                    try {
                        List<String> agencyTemps = new ArrayList<>(filteredAgencyIds);
                        int procedureUsed = countDistinctProcedureIds(fromDate, toDate, convertToObjectIdList(agencyTemps));
                        target.setProcedureUsed(procedureUsed);
                    } catch (Exception e) {
                        logger.warn("Error calculating procedureUsed for agency: " + agency.getId(), e);
                        target.setProcedureUsed(0);
                    }
                }
            }

            resultsAgency.add(target);
        }

        return resultsAgency.stream()
                .filter(this::hasAnyData)
                .sorted(agencyComparator)
                .collect(Collectors.toList());
    }

    /**
     * Helper method để aggregate data từ generalReport vào target
     */
    private void aggregateReportData(GeneralReportDto.CustomSummary target, GeneralReportDto generalReport) {
        target.setReceivedOnline(target.getReceivedOnline() + generalReport.getReceivedOnline());
        target.setReceivedDirect(target.getReceivedDirect() + generalReport.getReceivedDirect());
        target.setReceived(target.getReceived() + generalReport.getReceived());
        target.setReceivedOld(target.getReceivedOld() + generalReport.getReceivedOld());
        target.setUnresolvedOnTime(target.getUnresolvedOnTime() + generalReport.getUnresolvedOnTime());
        target.setUnresolvedOverdue(target.getUnresolvedOverdue() + generalReport.getUnresolvedOverdue());
        target.setResolvedEarly(target.getResolvedEarly() + generalReport.getResolvedEarly());
        target.setResolvedOnTime(target.getResolvedOnTime() + generalReport.getResolvedOnTime());
        target.setResolvedOverdue(target.getResolvedOverdue() + generalReport.getResolvedOverdue());
        target.setWithdraw(target.getWithdraw() + generalReport.getWithdraw());
        target.setResolved(target.getResolved() + generalReport.getResolved());
        target.setUnresolved(target.getUnresolved() + generalReport.getUnresolved());
        target.setDirect(target.getDirect() + generalReport.getDirect());
        target.setReceivedPostal(target.getReceivedPostal() + generalReport.getReceivedPostal());
        target.setReceivedPublicPostal(target.getReceivedPublicPostal() + generalReport.getReceivedPublicPostal());
        target.setReceivedSmartphone(target.getReceivedSmartphone() + generalReport.getReceivedSmartphone());
    }

    public Aggregation getAggregation(Date fromDate, Date toDate, List<ObjectId> agencyIds, Date previousDate, Integer reportType, List<String> sectorId, List<String> procedureId) throws JSONException {

        String[] option = new String[6];

        AggregationOperation matchOperation = Aggregation.match(buildCriteria(agencyIds, reportType, sectorId, procedureId, option));

        AggregationOperation projectOperation = context -> {
            Document projection = new Document();
            projection.put("tiep_nhan_truc_tuyen", getTiepNhanTrucTuyen(fromDate, toDate));
            projection.put("tiep_nhan_con_lai", getHoSoTiepNhanConLai(fromDate, toDate));
            projection.put("tiep_nhan_ky_truoc_giai_quyet_trong_han", getTiepNhanKyTruocGiaiQuyetTrongHan(previousDate));
            projection.put("tiep_nhan_ky_truoc_giai_quyet_qua_han", getTiepNhanKyTruocGiaiQuyetQuaHan(previousDate));
            projection.put("dang_giai_quyet_trong_han", getDangGiaiQuyetTrongHan(toDate));
            projection.put("dang_giai_quyet_qua_han", getDangGiaiQuyetQuaHan(toDate));
            projection.put("da_xu_ly_som_han", getDaXuLySomHanCondition(fromDate, toDate));
            projection.put("da_xu_ly_dung_han", getDaXuLyDungHanCondition(fromDate, toDate));
            projection.put("da_xu_ly_qua_han", getDaXuLyQuaHanCondition(fromDate, toDate));
            projection.put("ho_so_rut", getHoSoRutCondition(fromDate, toDate));
            projection.put("ho_so_truc_tiep", getHoSoTrucTiepCondition(fromDate, toDate));
            projection.put("ho_so_buu_chinh", getHoSoBuuChinhCondition(fromDate, toDate));
            projection.put("ho_so_buu_chinh_cong_ich", getHoSoBuuChinhCongIchCondition(fromDate, toDate));
            projection.put("ho_so_smart_phone", getHoSoSmartPhoneCondition(fromDate, toDate));

            projection.put(keyAddField(reportType), valueAddField(reportType));


            projection.put("ma_don_vi_cha", "$agencyAncestorsLevel1Id");
            projection.put("ten_don_vi_con", "$agencyName");

            return new Document("$addFields", projection);
        };

        AggregationOperation groupOperation = Aggregation.group(option[3])
                .sum("tiep_nhan_truc_tuyen").as("receivedOnline")
                .sum("tiep_nhan_con_lai").as("receivedDirect")
                .sum("tiep_nhan_ky_truoc_giai_quyet_trong_han").as("tiep_nhan_ky_truoc_giai_quyet_trong_han")
                .sum("tiep_nhan_ky_truoc_giai_quyet_qua_han").as("tiep_nhan_ky_truoc_giai_quyet_qua_han")
                .sum("dang_giai_quyet_trong_han").as("unresolvedOnTime")
                .sum("dang_giai_quyet_qua_han").as("unresolvedOverdue")
                .sum("da_xu_ly_som_han").as("resolvedEarly")
                .sum("da_xu_ly_dung_han").as("resolvedOnTime")
                .sum("da_xu_ly_qua_han").as("resolvedOverdue")
                .sum("ho_so_rut").as("withdraw")
                .sum("ho_so_truc_tiep").as("direct")
                .sum("ho_so_buu_chinh").as("receivedPostal")
                .sum("ho_so_buu_chinh_cong_ich").as("receivedPublicPostal")
                .sum("ho_so_smart_phone").as("receivedSmartphone")
                .first(keyAddField(reportType)).as(keyAddField(reportType))
                .first("ma_don_vi_cha").as("ma_don_vi_cha")
                .first("ten_don_vi_con").as("ten_don_vi_con");

        AggregationOperation finalProjectOperation = Aggregation.project()
                .andExclude("_id")
                .and("_id").as(option[0])
                .and(option[1]).as(option[2])
                .and("ma_don_vi_cha").as("agency.idAgency")
                .and("ten_don_vi_con").as("agencyBaby.name")
                .and("receivedOnline").as("receivedOnline")
                .and("receivedDirect").as("receivedDirect")
                .andExpression("tiep_nhan_ky_truoc_giai_quyet_trong_han + tiep_nhan_ky_truoc_giai_quyet_qua_han + receivedOnline + receivedDirect").as("received")
                .and("tiep_nhan_ky_truoc_giai_quyet_trong_han").plus("tiep_nhan_ky_truoc_giai_quyet_qua_han").as("receivedOld")
                .and("unresolvedOnTime").as("unresolvedOnTime")
                .and("unresolvedOverdue").as("unresolvedOverdue")
                .and("resolvedEarly").as("resolvedEarly")
                .and("resolvedOnTime").as("resolvedOnTime")
                .and("resolvedOverdue").as("resolvedOverdue")
                .and("withdraw").as("withdraw")
                .andExpression("resolvedEarly + resolvedOnTime + resolvedOverdue").as("resolved")
                .andExpression("unresolvedOnTime + unresolvedOverdue").as("unresolved")
                .and("direct").as("direct")
                .and("receivedPostal").as("receivedPostal")
                .and("receivedPublicPostal").as("receivedPublicPostal")
                .and("receivedSmartphone").as("receivedSmartphone");

        return Aggregation.newAggregation(
                matchOperation,
                projectOperation,
                groupOperation,
                finalProjectOperation
        );
    }

    private Set<ObjectId> uniqueObjectIdAgency(String stringIds) {
        if (stringIds == null || stringIds.trim().isEmpty()) {
            return Collections.emptySet();
        }

        return Arrays.stream(stringIds.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .map(s -> {
                    try {
                        return new ObjectId(s);
                    } catch (IllegalArgumentException e) {
                        logger.warn("Invalid ObjectId: " + s, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
    }

    private Set<String> uniqueStringAgency(String stringIds) {
        if (stringIds == null || stringIds.trim().isEmpty()) {
            return Collections.emptySet();
        }

        return Arrays.stream(stringIds.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .collect(Collectors.toSet());
    }

    private boolean hasAnyData(GeneralReportDto dto) {
        return dto.getReceivedOnline() > 0 ||
               dto.getReceivedDirect() > 0 ||
               dto.getReceived() > 0 ||
               dto.getReceivedOld() > 0 ||
               dto.getUnresolvedOnTime() > 0 ||
               dto.getUnresolvedOverdue() > 0 ||
               dto.getResolvedEarly() > 0 ||
               dto.getResolvedOnTime() > 0 ||
               dto.getResolvedOverdue() > 0 ||
               dto.getWithdraw() > 0 ||
               dto.getResolved() > 0 ||
               dto.getUnresolved() > 0 ||
               dto.getDirect() > 0 ||
               dto.getReceivedPostal() > 0 ||
               dto.getReceivedPublicPostal() > 0 ||
               dto.getReceivedSmartphone() > 0;
    }

    private boolean hasAnyData(GeneralReportDto.CustomSummary summary) {
        return summary.getReceivedOnline() > 0 ||
               summary.getReceivedDirect() > 0 ||
               summary.getReceived() > 0 ||
               summary.getReceivedOld() > 0 ||
               summary.getUnresolvedOnTime() > 0 ||
               summary.getUnresolvedOverdue() > 0 ||
               summary.getResolvedEarly() > 0 ||
               summary.getResolvedOnTime() > 0 ||
               summary.getResolvedOverdue() > 0 ||
               summary.getWithdraw() > 0 ||
               summary.getResolved() > 0 ||
               summary.getUnresolved() > 0 ||
               summary.getDirect() > 0 ||
               summary.getReceivedPostal() > 0 ||
               summary.getReceivedPublicPostal() > 0 ||
               summary.getReceivedSmartphone() > 0;
    }

    private GeneralReportDto.CustomSummary mergeCustomSummary(
            GeneralReportDto.CustomSummary data1,
            GeneralReportDto.CustomSummary data2,
            GeneralReportDto.Procedure procedure,
            GeneralReportDto.Sector sector,
            GeneralReportDto.Agency agency) {

        GeneralReportDto.CustomSummary merged = new GeneralReportDto.CustomSummary(
                procedure, sector, agency,
                data1.getReceivedOnline() + data2.getReceivedOnline(),
                data1.getReceivedDirect() + data2.getReceivedDirect(),
                data1.getReceived() + data2.getReceived(),
                data1.getReceivedOld() + data2.getReceivedOld(),
                data1.getUnresolvedOnTime() + data2.getUnresolvedOnTime(),
                data1.getUnresolvedOverdue() + data2.getUnresolvedOverdue(),
                data1.getResolvedEarly() + data2.getResolvedEarly(),
                data1.getResolvedOnTime() + data2.getResolvedOnTime(),
                data1.getResolvedOverdue() + data2.getResolvedOverdue(),
                data1.getWithdraw() + data2.getWithdraw(),
                data1.getResolved() + data2.getResolved(),
                data1.getUnresolved() + data2.getUnresolved(),
                data1.getDirect() + data2.getDirect(),
                data1.getReceivedPostal() + data2.getReceivedPostal(),
                data1.getReceivedPublicPostal() + data2.getReceivedPublicPostal(),
                data1.getReceivedSmartphone() + data2.getReceivedSmartphone(),
                data1.getProcedureUsed() + data2.getProcedureUsed()
        );
        return merged;
    }

    private List<GeneralReportDto.CustomSummary> groupBySector(List<GeneralReportDto.CustomSummary> results) {
        Map<String, GeneralReportDto.CustomSummary> resultMap = results.stream()
                .collect(Collectors.toMap(
                        data -> data.getSector().getName(),
                        data -> data,
                        (data1, data2) -> mergeCustomSummary(
                                data1, data2,
                                null,
                                new GeneralReportDto.Sector(
                                        data1.getSector().getIdSector() + "," + data2.getSector().getIdSector(),
                                        data1.getSector().getName(),
                                        data1.getSector().getCode()
                                ),
                                null
                        ),
                        LinkedHashMap::new
                ));
        return new ArrayList<>(resultMap.values());
    }

    private List<GeneralReportDto.CustomSummary> groupByProcedure(List<GeneralReportDto.CustomSummary> results) {
        Map<String, GeneralReportDto.CustomSummary> resultMap = results.stream()
                .collect(Collectors.toMap(
                        data -> data.getProcedure().getName(),
                        data -> data,
                        (data1, data2) -> mergeCustomSummary(
                                data1, data2,
                                new GeneralReportDto.Procedure(
                                        data1.getProcedure().getIdProcedure() + "," + data2.getProcedure().getIdProcedure(),
                                        data1.getProcedure().getName(),
                                        data1.getProcedure().getCode()
                                ),
                                null,
                                null
                        ),
                        LinkedHashMap::new
                ));
        return new ArrayList<>(resultMap.values());
    }

    private void processResultSecurity(List<DetailGeneralReportDto.PageResult> results, Pageable pageable) {
        List<String> rolesToCheck = List.of(adminRoles.split(","));
        boolean isAdmin = Context.getListPemission(rolesToCheck);

        if (!isAdmin && enableHideName) {
            // Ẩn thông tin bảo mật và gán số thứ tự
            int startIndex = pageable != null ? pageable.getPageNumber() * pageable.getPageSize() : 0;
            IntStream.range(0, results.size())
                    .forEach(i -> {
                        DetailGeneralReportDto.PageResult item = results.get(i);
                        item.setHideSecurityInformation(item.getApplicantOwnerFullName(), item.getAssigneeFullname());
                        item.setNo(startIndex + i + 1);
                    });
        } else {
            int startIndex = pageable != null ? pageable.getPageNumber() * pageable.getPageSize() : 0;
            IntStream.range(0, results.size())
                    .forEach(i -> results.get(i).setNo(startIndex + i + 1));
        }
    }

    private void processResultSecurity(List<DetailGeneralReportDto.PageResult> results) {
        processResultSecurity(results, null);
    }

    private void aggregateReportData(GeneralReportDto.CustomSummary target, GeneralReportDto source) {
        target.setReceivedOnline(target.getReceivedOnline() + source.getReceivedOnline());
        target.setReceivedDirect(target.getReceivedDirect() + source.getReceivedDirect());
        target.setReceived(target.getReceived() + source.getReceived());
        target.setReceivedOld(target.getReceivedOld() + source.getReceivedOld());
        target.setUnresolvedOnTime(target.getUnresolvedOnTime() + source.getUnresolvedOnTime());
        target.setUnresolvedOverdue(target.getUnresolvedOverdue() + source.getUnresolvedOverdue());
        target.setResolvedEarly(target.getResolvedEarly() + source.getResolvedEarly());
        target.setResolvedOnTime(target.getResolvedOnTime() + source.getResolvedOnTime());
        target.setResolvedOverdue(target.getResolvedOverdue() + source.getResolvedOverdue());
        target.setWithdraw(target.getWithdraw() + source.getWithdraw());
        target.setResolved(target.getResolved() + source.getResolved());
        target.setUnresolved(target.getUnresolved() + source.getUnresolved());
        target.setDirect(target.getDirect() + source.getDirect());
        target.setReceivedPostal(target.getReceivedPostal() + source.getReceivedPostal());
        target.setReceivedPublicPostal(target.getReceivedPublicPostal() + source.getReceivedPublicPostal());
        target.setReceivedSmartphone(target.getReceivedSmartphone() + source.getReceivedSmartphone());
    }

    private List<ObjectId> processAgencyIds(List<String> agencyIds) {
        StringBuilder agencyStringBuilder = new StringBuilder();
        for (String agencyTemp : agencyIds) {
            try {
                var target = agencyFilterReportQniService.getDocumentsByAgencyId(agencyTemp);
                if (Objects.nonNull(target)) {
                    agencyStringBuilder.append(target.getIdFilter()).append(",");
                }
            } catch (Exception ex) {
                logger.warn("Error processing agency ID: " + agencyTemp, ex);
            }
        }
        var uniqueAgency = uniqueObjectIdAgency(agencyStringBuilder.toString());
        return new ArrayList<>(uniqueAgency);
    }

    private Date[] parseDates(String fromDateString, String toDateString) throws Exception {
        this.df.setTimeZone(this.timezone);
        Date fromDate = this.df.parse(fromDateString);
        Date toDate = this.df.parse(toDateString);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(fromDate);
        calendar.add(Calendar.DAY_OF_YEAR, -1);
        Date previousDate = calendar.getTime();

        return new Date[]{fromDate, toDate, previousDate};
    }

    public List<GeneralReportDto.CustomSummary> getGeneralReportDto(List<String> agencyIds, String fromDateString,
                                                                    String toDateString, Integer reportType, List<String> sectorId, List<String> procedureId,
                                                                    Boolean includeInactiveAgency,
                                                                    Boolean isDVC) {
        List<GeneralReportDto.CustomSummary> results = new ArrayList<>();
        logger.info("Begin getGeneralReportByAgencyDto");

        try {
            List<ObjectId> agencyObjectIds = processAgencyIds(agencyIds);
            Date[] dates = parseDates(fromDateString, toDateString);
            Date fromDate = dates[0];
            Date toDate = dates[1];
            Date previousDate = dates[2];

            if (reportType == null) {
                reportType = 0;
            }

            Aggregation aggregation = getAggregation(fromDate, toDate, agencyObjectIds, previousDate, reportType, sectorId, procedureId);
            AggregationResults<GeneralReportDto> resultAggregation = mongoTemplate.aggregate(aggregation, "flattenedDossier", GeneralReportDto.class);
            List<GeneralReportDto> generalReportDto = resultAggregation.getMappedResults();

            Comparator<GeneralReportDto.CustomSummary> procedureComparator = Comparator.comparing(procedure -> procedure.getProcedure().getName());
            Comparator<GeneralReportDto.CustomSummary> sectorComparator = Comparator.comparing(sector -> sector.getSector().getName());

            if (reportType == 0) {
                if (Objects.nonNull(isDVC) && isDVC) {
                    return getReportWithAgencyForDVC(agencyIds, generalReportDto, includeInactiveAgency, fromDate, toDate);
                } else {
                    return getReportWithAgency(agencyIds, generalReportDto, includeInactiveAgency, isDVC, fromDate, toDate);
                }
            }

            var generalReportDtoModify = generalReportDto.stream().filter(this::hasAnyData);

            results = generalReportDtoModify
                    .map(GeneralReportDto::toCustomObject)
                    .sorted(reportType == 1 ? sectorComparator : procedureComparator)
                    .collect(Collectors.toList());

            if (reportType == 1) {
                results = groupBySector(results);
            } else if (reportType == 2) {
                results = groupByProcedure(results);
            }

            return results;

        } catch (Exception e) {
            logger.error("Error getGeneralReportByAgencyDto: " + e.getMessage());
        }

        logger.info("End getGeneralReportByAgencyDto");
        return results;
    }

    public Page<DetailGeneralReportDto.PageResult> getGeneralReportDetailDto(List<String> agencyIds,
                                                                             String fromDateString,
                                                                             String toDateString,
                                                                             Integer reportType,
                                                                             List<String> sectorId,
                                                                             List<String> procedureId,
                                                                             Integer type,
                                                                             Pageable pageable) {
        logger.info("Begin getGeneralReportDetailDto");

        try {
            Date[] dates = parseDates(fromDateString, toDateString);
            Date fromDate = dates[0];
            Date toDate = dates[1];
            Date previousDate = dates[2];

            if (reportType == null) {
                reportType = 0;
            }

            List<ObjectId> agencyObjectIds = processAgencyIds(agencyIds);

            Aggregation aggregation = getAggregationDetail(fromDate, toDate, agencyObjectIds, previousDate, reportType, sectorId, procedureId, type, pageable, false);
            AggregationResults<DetailGeneralReportDto> resultAggregation = mongoTemplate.aggregate(aggregation, "flattenedDossier", DetailGeneralReportDto.class);

            List<DetailGeneralReportDto.PageResult> results = resultAggregation.getMappedResults().get(0).getPagedResults();
            long totalCount = 0;
            if (!results.isEmpty()) {
                totalCount = resultAggregation.getMappedResults().get(0).getTotalCount().get(0).getTotalCount();
            }

            processResultSecurity(results, pageable);
            return new PageImpl<>(results, pageable, totalCount);

        } catch (Exception e) {
            logger.error("Error getGeneralReportByAgencyDto: " + e.getMessage());
        }

        logger.info("End getGeneralReportDetailDto");
        return null;
    }

    public List<DetailGeneralReportDto.PageResult> getAllGeneralReportDetailDto(List<String> agencyIds,
                                                                                String fromDateString,
                                                                                String toDateString,
                                                                                Integer reportType,
                                                                                List<String> sectorId,
                                                                                List<String> procedureId,
                                                                                Integer type) {
        logger.info("Begin getAllGeneralReportDetailDto");

        try {
            // Tối ưu: Sử dụng extracted methods
            Date[] dates = parseDates(fromDateString, toDateString);
            Date fromDate = dates[0];
            Date toDate = dates[1];
            Date previousDate = dates[2];

            if (reportType == null) {
                reportType = 0;
            }

            List<ObjectId> agencyObjectIds = processAgencyIds(agencyIds);

            Aggregation aggregation = getAggregationDetail(fromDate, toDate, agencyObjectIds, previousDate, reportType, sectorId, procedureId, type, null, true);

            AggregationResults<DetailGeneralReportDto.PageResult> resultAggregation = mongoTemplate.aggregate(aggregation, "flattenedDossier", DetailGeneralReportDto.PageResult.class);
            List<DetailGeneralReportDto.PageResult> results = resultAggregation.getMappedResults();
            // Tối ưu: Sử dụng helper method
            processResultSecurity(results);
            return results;

        } catch (Exception e) {
            logger.error("Error getAllGeneralReportByAgencyDto: " + e.getMessage());
        }

        logger.info("End getAllGeneralReportDetailDto");
        return null;
    }

    public List<DetailGeneralReportDto.PageResult> getDataExcelDto(List<String> agencyIds,
                                                                   String fromDateString,
                                                                   String toDateString,
                                                                   Integer reportType,
                                                                   List<String> sectorId,
                                                                   List<String> procedureId,
                                                                   Integer type) {
        logger.info("Begin getGeneralReportDetailDto");

        try {
            // Tối ưu: Sử dụng extracted methods
            Date[] dates = parseDates(fromDateString, toDateString);
            Date fromDate = dates[0];
            Date toDate = dates[1];
            Date previousDate = dates[2];

            if (reportType == null) {
                reportType = 0;
            }

            List<ObjectId> agencyObjectIds = processAgencyIds(agencyIds);

            Aggregation aggregation = getAggregationDetail(fromDate, toDate, agencyObjectIds, previousDate, reportType, sectorId, procedureId, type, null, true);

            AggregationResults<DetailGeneralReportDto.PageResult> resultAggregation = mongoTemplate.aggregate(aggregation, "flattenedDossier", DetailGeneralReportDto.PageResult.class);
            List<DetailGeneralReportDto.PageResult> results = resultAggregation.getMappedResults();

            // Tối ưu: Sử dụng helper method
            processResultSecurity(results);

            return results;

        } catch (Exception e) {
            logger.error("Error getGeneralReportByAgencyDto: " + e.getMessage());
        }

        logger.info("End getGeneralReportDetailDto");
        return null;
    }

    public ResponseEntity<Object> exportGeneralReportDetail(String fromDate, String toDate,
                                                            List<String> agencyIds,
                                                            List<String> sectorId, List<String> procedureId,
                                                            Integer type, Integer reportType) {
        Date date = new Date();
        TimeZone timezone = TimeZone.getTimeZone("Asia/Ho_Chi_Minh");
        DateFormat dfTimestamp = new SimpleDateFormat("yyyyMMddHHmm");
        DateFormat dfCurrentDate = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss");
        dfTimestamp.setTimeZone(timezone);
        dfCurrentDate.setTimeZone(timezone);
        String timestamp = dfTimestamp.format(date);
        String currentDate = dfCurrentDate.format(date);
        String fromDateReport = fromDate.substring(8, 10) + "/" + fromDate.substring(5, 7) + "/" + fromDate.substring(0, 4);
        String toDateReport = toDate.substring(8, 10) + "/" + toDate.substring(5, 7) + "/" + toDate.substring(0, 4);
        String filename = timestamp + "-danh-sach-ho-so.xlsx";
        byte[] resource = new byte[0];
        try {
            InputStream is = resourceTemplateDossierStatisticAssigneeQNIV2.getInputStream();
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            org.jxls.common.Context context = new org.jxls.common.Context();

            var itemDtos = getDataExcelDto(agencyIds, fromDate, toDate, reportType, sectorId, procedureId, type);

            context.putVar("textBanner", translator.toLocale("lang.word.gov"));
            context.putVar("subtextBanner", translator.toLocale("lang.word.ifh"));

            context.putVar("title", translator.toLocale("lang.word.dossier-statistic-title"));
            context.putVar("subTitle", translator.toLocale("lang.word.dossier-statistic-fromdate-todate", new String[]{fromDateReport, toDateReport}));
            context.putVar("currentDate", translator.toLocale("lang.word.dossier-statistic-current-date", new String[]{currentDate}));
            context.putVar("no", translator.toLocale("lang.word.no"));
            context.putVar("dossierCode", translator.toLocale("lang.word.dossier-statistic-dossier-code"));
            context.putVar("procedureName", translator.toLocale("lang.word.dossier-statistic-procedure-name"));
            context.putVar("sectorName", translator.toLocale("lang.word.dossier-statistic-sector-name"));
            context.putVar("noiDungYeuCauGiaiQuyet", translator.toLocale("lang.word.dossier-statistic-noidungyeucaugiaiquyet"));
            context.putVar("acceptedDate", translator.toLocale("lang.word.dossier-statistic-accepted-date"));
            context.putVar("appointmentDate", translator.toLocale("lang.word.dossier-statistic-appointment-date"));
            context.putVar("completedDate", translator.toLocale("lang.word.dossier-statistic-completed-date"));
            context.putVar("applicantOwnerFullName", translator.toLocale("lang.word.dossier-statistic-applicant-ownerfullname"));
            context.putVar("applicantPhoneNumber", translator.toLocale("lang.word.dossier-statistic-applicant-phonenumber"));
            context.putVar("assigneeFullname", translator.toLocale("lang.word.dossier-statistic-assignee-fullname"));
            context.putVar("dossierStatusName", translator.toLocale("lang.word.dossier-statistic-status-name"));
            context.putVar("applyMethod", translator.toLocale("lang.word.dossier-statistic-applied-method"));
            context.putVar("writer", translator.toLocale("lang.word.dossier-statistic-writer"));
            context.putVar("reporter", translator.toLocale("lang.word.dossier-statistic-reporter"));
            context.putVar("itemDtos", itemDtos);

            XlsCommentAreaBuilder.addCommandMapping("autoRowHeight", AutoRowHeightCommand.class);
            JxlsHelper.getInstance().processTemplateAtCell(is, outputStream, context, "Worksheet!A1");
            resource = outputStream.toByteArray();
        } catch (Exception ex) {
            logger.info("exportDossierStatistic012020Detail error:" + ex.getMessage());
        }

        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename)
                .contentType(MediaType.parseMediaType("application/vnd.ms-excel"))
                .body(resource);
    }

}
